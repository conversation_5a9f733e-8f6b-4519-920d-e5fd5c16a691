<?php

if (!function_exists('render_navigation_menu')) {
    /**
     * Render navigation menu as HTML
     *
     * @param array $menus
     * @param string $class
     * @param int $level
     * @return string
     */
    function render_navigation_menu($menus, $class = 'navbar-nav', $level = 0)
    {
        if (empty($menus)) {
            return '';
        }

        $html = '<ul class="' . $class . '">';

        foreach ($menus as $menu) {
            $html .= render_menu_item($menu, $level);
        }

        $html .= '</ul>';

        return $html;
    }
}

if (!function_exists('render_sidebar_navigation')) {
    /**
     * Render sidebar navigation menu as HTML
     *
     * @param array $menus
     * @return string
     */
    function render_sidebar_navigation($menus)
    {
        if (empty($menus)) {
            return '';
        }

        $html = '';

        foreach ($menus as $menu) {
            $html .= render_sidebar_menu_item($menu);
        }

        return $html;
    }
}

if (!function_exists('render_icon')) {
    /**
     * Render icon SVG
     *
     * @param string $iconClass
     * @return string
     */
    function render_icon($iconClass)
    {
        // Map common icon classes to SVG paths
        $iconMap = [
            'ti ti-home' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />',
            'ti ti-package' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5" /><path d="M12 12l8 -4.5" /><path d="M12 12l0 9" /><path d="M12 12l-8 -4.5" />',
            'ti ti-menu-2' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 6l16 0" /><path d="M4 12l16 0" /><path d="M4 18l16 0" />',
            'ti ti-users' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" /><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" /><path d="M16 3.13a4 4 0 0 1 0 7.75" /><path d="M21 21v-2a4 4 0 0 0 -3 -3.85" />',
            'ti ti-list' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6l11 0" /><path d="M9 12l11 0" /><path d="M9 18l11 0" /><path d="M5 6l0 .01" /><path d="M5 12l0 .01" /><path d="M5 18l0 .01" />',
            'ti ti-user-plus' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0" /><path d="M16 19h6" /><path d="M19 16v6" /><path d="M6 21v-2a4 4 0 0 1 4 -4h4" />',
            'ti ti-settings' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" /><path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" />',
            'ti ti-chart-bar' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" /><path d="M9 8m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" /><path d="M15 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />',
            'ti ti-chart-line' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 19l16 0" /><path d="M4 15l4 -6l4 2l4 -5l4 4" />',
            'ti ti-activity' => '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 12h4l3 8l4 -16l3 8h4" />',
        ];

        $svgPath = $iconMap[$iconClass] ?? '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />';

        return '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">' . $svgPath . '</svg>';
    }
}

if (!function_exists('render_sidebar_menu_item')) {
    /**
     * Render individual sidebar menu item
     *
     * @param array $menu
     * @return string
     */
    function render_sidebar_menu_item($menu)
    {
        $hasChildren = !empty($menu['children']);
        $isActive = is_menu_active($menu);

        $html = '';

        if ($hasChildren) {
            // Dropdown menu
            $html .= '<li class="nav-item dropdown">';
            $html .= '<a class="nav-link dropdown-toggle' . ($isActive ? ' show' : '') . '" href="#navbar-' . $menu['id'] . '" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="' . ($isActive ? 'true' : 'false') . '">';
            if ($menu['icon']) {
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= render_icon($menu['icon']);
                $html .= '</span>';
            }
            $html .= '<span class="nav-link-title">' . esc($menu['title']) . '</span>';
            $html .= '</a>';

            $html .= '<div class="dropdown-menu' . ($isActive ? ' show' : '') . '">';
            foreach ($menu['children'] as $child) {
                $childActive = is_menu_active($child);
                $html .= '<a class="dropdown-item' . ($childActive ? ' active' : '') . '" href="' . get_menu_url($child) . '"';
                if ($child['target'] !== '_self') {
                    $html .= ' target="' . $child['target'] . '"';
                }
                $html .= '>';
                if ($child['icon']) {
                    $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block me-2">';
                    $html .= render_icon($child['icon']);
                    $html .= '</span>';
                }
                $html .= esc($child['title']);
                $html .= '</a>';
            }
            $html .= '</div>';
            $html .= '</li>';
        } else {
            // Regular menu item
            $html .= '<li class="nav-item">';
            $html .= '<a class="nav-link' . ($isActive ? ' active' : '') . '" href="' . get_menu_url($menu) . '"';
            if ($menu['target'] !== '_self') {
                $html .= ' target="' . $menu['target'] . '"';
            }
            $html .= '>';
            if ($menu['icon']) {
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= render_icon($menu['icon']);
                $html .= '</span>';
            }
            $html .= '<span class="nav-link-title">' . esc($menu['title']) . '</span>';
            $html .= '</a>';
            $html .= '</li>';
        }

        return $html;
    }
}

if (!function_exists('render_menu_item')) {
    /**
     * Render individual menu item
     *
     * @param array $menu
     * @param int $level
     * @return string
     */
    function render_menu_item($menu, $level = 0)
    {
        $hasChildren = !empty($menu['children']);
        $isActive = is_menu_active($menu);
        
        $liClass = 'nav-item';
        if ($hasChildren) {
            $liClass .= ' dropdown';
        }
        if ($isActive) {
            $liClass .= ' active';
        }
        if ($menu['css_class']) {
            $liClass .= ' ' . $menu['css_class'];
        }
        
        $html = '<li class="' . $liClass . '">';
        
        if ($hasChildren) {
            // Dropdown menu
            $html .= '<a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" role="button" aria-expanded="false">';
            if ($menu['icon']) {
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= '<i class="' . $menu['icon'] . '"></i>';
                $html .= '</span>';
            }
            $html .= '<span class="nav-link-title">' . esc($menu['title']) . '</span>';
            $html .= '</a>';
            
            $html .= '<div class="dropdown-menu">';
            foreach ($menu['children'] as $child) {
                $html .= '<a class="dropdown-item" href="' . get_menu_url($child) . '"';
                if ($child['target'] !== '_self') {
                    $html .= ' target="' . $child['target'] . '"';
                }
                $html .= '>';
                if ($child['icon']) {
                    $html .= '<i class="' . $child['icon'] . ' me-2"></i>';
                }
                $html .= esc($child['title']);
                $html .= '</a>';
            }
            $html .= '</div>';
        } else {
            // Regular menu item
            $html .= '<a class="nav-link" href="' . get_menu_url($menu) . '"';
            if ($menu['target'] !== '_self') {
                $html .= ' target="' . $menu['target'] . '"';
            }
            $html .= '>';
            if ($menu['icon']) {
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= '<i class="' . $menu['icon'] . '"></i>';
                $html .= '</span>';
            }
            $html .= '<span class="nav-link-title">' . esc($menu['title']) . '</span>';
            $html .= '</a>';
        }
        
        $html .= '</li>';
        
        return $html;
    }
}

if (!function_exists('get_menu_url')) {
    /**
     * Get menu URL
     *
     * @param array $menu
     * @return string
     */
    function get_menu_url($menu)
    {
        if (empty($menu['url'])) {
            return '#';
        }
        
        // If URL starts with http or https, return as is
        if (preg_match('/^https?:\/\//', $menu['url'])) {
            return $menu['url'];
        }
        
        // If URL starts with /, treat as absolute path
        if (strpos($menu['url'], '/') === 0) {
            return base_url($menu['url']);
        }
        
        // Otherwise, treat as relative to base URL
        return base_url($menu['url']);
    }
}

if (!function_exists('is_menu_active')) {
    /**
     * Check if menu item is active
     *
     * @param array $menu
     * @return bool
     */
    function is_menu_active($menu)
    {
        if (empty($menu['url'])) {
            return false;
        }
        
        $currentUrl = current_url();
        $menuUrl = get_menu_url($menu);
        
        // Exact match
        if ($currentUrl === $menuUrl) {
            return true;
        }
        
        // Check if current URL starts with menu URL (for parent menus)
        if (strpos($currentUrl, rtrim($menuUrl, '/')) === 0) {
            return true;
        }
        
        return false;
    }
}

if (!function_exists('render_breadcrumb')) {
    /**
     * Render breadcrumb navigation
     *
     * @param array $breadcrumb
     * @return string
     */
    function render_breadcrumb($breadcrumb)
    {
        if (empty($breadcrumb)) {
            return '';
        }
        
        $html = '<nav aria-label="breadcrumb">';
        $html .= '<ol class="breadcrumb">';
        
        $total = count($breadcrumb);
        foreach ($breadcrumb as $index => $item) {
            $isLast = ($index === $total - 1);
            
            if ($isLast) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">';
                $html .= esc($item['title']);
                $html .= '</li>';
            } else {
                $html .= '<li class="breadcrumb-item">';
                $html .= '<a href="' . get_menu_url($item) . '">' . esc($item['title']) . '</a>';
                $html .= '</li>';
            }
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
}

if (!function_exists('get_navigation_menus')) {
    /**
     * Get navigation menus from database
     *
     * @param bool $activeOnly
     * @return array
     */
    function get_navigation_menus($activeOnly = true)
    {
        $navigationModel = new \Modules\NavigationMenu\Models\NavigationMenuModel();
        return $navigationModel->getMenuTree(null, $activeOnly);
    }
}

if (!function_exists('format_file_size')) {
    /**
     * Format file size in human readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    function format_file_size($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('time_ago')) {
    /**
     * Get time ago string
     *
     * @param string $datetime
     * @return string
     */
    function time_ago($datetime)
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) {
            return 'just now';
        } elseif ($time < 3600) {
            return floor($time / 60) . ' minutes ago';
        } elseif ($time < 86400) {
            return floor($time / 3600) . ' hours ago';
        } elseif ($time < 2592000) {
            return floor($time / 86400) . ' days ago';
        } elseif ($time < 31536000) {
            return floor($time / 2592000) . ' months ago';
        } else {
            return floor($time / 31536000) . ' years ago';
        }
    }
}
