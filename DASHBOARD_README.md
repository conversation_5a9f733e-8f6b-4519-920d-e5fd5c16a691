# Professional Dashboard with Tabler.io and CodeIgniter 4

This project provides a complete professional dashboard built with Tabler.io UI framework and CodeIgniter 4, featuring a modular navigation menu management system.

## Features

- **Professional Dashboard**: Clean, modern interface using Tabler.io components
- **Modular Architecture**: Navigation menu module with full CRUD operations
- **Responsive Design**: Mobile-friendly layout with collapsible navigation
- **Dynamic Navigation**: Database-driven menu system with hierarchy support
- **Admin Layout**: Reusable admin layout with header, sidebar, and footer
- **Helper Functions**: Utility functions for menu rendering and breadcrumbs

## Project Structure

```
├── app/
│   ├── Controllers/Admin/
│   │   └── DashboardController.php
│   ├── Views/
│   │   ├── layouts/
│   │   │   └── admin.php
│   │   └── admin/
│   │       └── dashboard.php
│   ├── Helpers/
│   │   └── navigation_helper.php
│   └── Config/
│       ├── Autoload.php
│       └── Routes.php
├── Modules/
│   └── NavigationMenu/
│       ├── Controllers/
│       │   └── NavigationController.php
│       ├── Models/
│       │   └── NavigationMenuModel.php
│       ├── Views/
│       │   ├── index.php
│       │   ├── create.php
│       │   └── edit.php
│       ├── Database/
│       │   ├── Migrations/
│       │   │   └── 2024-01-01-000001_CreateNavigationMenusTable.php
│       │   └── Seeds/
│       │       └── NavigationMenuSeeder.php
│       └── Config/
│           └── Routes.php
└── public/
    └── tabler/
        ├── css/
        ├── js/
        └── img/
```

## Installation & Setup

### 1. Database Setup

Run the migration to create the navigation menus table:

```bash
php spark migrate -all
```

### 2. Seed Sample Data (Optional)

To populate the navigation menu with sample data:

```bash
php spark db:seed Modules\\NavigationMenu\\Database\\Seeds\\NavigationMenuSeeder
```

### 3. Configure Database

Make sure your database configuration is set up in `app/Config/Database.php`.

### 4. Access the Dashboard

- **Dashboard**: `/admin` or `/admin/dashboard`
- **Navigation Management**: `/admin/navigation`

## Navigation Menu Features

### Database Schema

The `navigation_menus` table includes:

- `id`: Primary key
- `parent_id`: For hierarchical menu structure
- `title`: Menu item title
- `url`: Menu item URL (optional for parent items)
- `icon`: CSS class for icons (supports Tabler icons)
- `target`: Link target (_self, _blank, _parent, _top)
- `sort_order`: Order of menu items
- `is_active`: Enable/disable menu items
- `permission`: Permission required to view menu (optional)
- `css_class`: Additional CSS classes
- `created_at` / `updated_at`: Timestamps

### Menu Management

1. **List Menus**: View all navigation menus in hierarchical structure
2. **Create Menu**: Add new menu items with parent-child relationships
3. **Edit Menu**: Modify existing menu items
4. **Delete Menu**: Remove menu items (prevents deletion if has children)
5. **Reorder**: Drag and drop functionality for menu ordering

### Helper Functions

The navigation helper provides several utility functions:

- `render_navigation_menu()`: Render complete navigation menu
- `render_menu_item()`: Render individual menu item
- `get_menu_url()`: Generate proper URLs for menu items
- `is_menu_active()`: Check if menu item is currently active
- `render_breadcrumb()`: Generate breadcrumb navigation
- `get_navigation_menus()`: Fetch menus from database

## Dashboard Components

### Statistics Cards
- Display key metrics with trend indicators
- Configurable colors and icons
- Responsive grid layout

### Charts
- Sales overview chart using ApexCharts
- Traffic sources donut chart
- Customizable data and styling

### Activity Feed
- Recent user activities
- Avatar support
- Time-based formatting

### Quick Actions
- Shortcut buttons for common tasks
- Icon-based interface
- Customizable action links

## Customization

### Adding New Menu Items

1. **Via Interface**: Use `/admin/navigation/create`
2. **Via Database**: Insert directly into `navigation_menus` table
3. **Via Seeder**: Add to `NavigationMenuSeeder.php`

### Styling

The dashboard uses Tabler.io CSS framework. Customize by:

1. Modifying `public/tabler/css/` files
2. Adding custom CSS in the admin layout
3. Using Tabler's utility classes

### Adding New Dashboard Widgets

1. Modify `DashboardController::getDashboardStats()`
2. Update the dashboard view template
3. Add corresponding JavaScript for interactive elements

## Security Considerations

- **Permission System**: Menu items support permission-based visibility
- **CSRF Protection**: All forms include CSRF tokens
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Using CodeIgniter's Query Builder

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Dependencies

- **CodeIgniter 4**: PHP framework
- **Tabler.io**: UI framework
- **ApexCharts**: Charting library
- **Bootstrap 5**: CSS framework (included with Tabler)

## Troubleshooting

### Common Issues

1. **Navigation not showing**: Check database connection and migration status
2. **Icons not displaying**: Verify Tabler CSS files are loaded
3. **JavaScript errors**: Check browser console and ensure all JS files are loaded
4. **Permission errors**: Verify file permissions for writable directories

### Debug Mode

Enable debug mode in `app/Config/Boot/development.php` for detailed error messages.

## Contributing

1. Follow CodeIgniter 4 coding standards
2. Use PSR-4 autoloading for new classes
3. Include proper documentation for new features
4. Test all functionality before submitting

## License

This project is open source and available under the [MIT License](LICENSE).
