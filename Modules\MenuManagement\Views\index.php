<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <div class="page-pretitle">
                    System
                </div>
                <h2 class="page-title">
                    Menu Management
                </h2>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <?php if (hasPermission('menu.manage')) : ?>
                    <a href="<?= route_to('menu.create') ?>" class="btn btn-primary d-none d-sm-inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M12 5l0 14"/>
                            <path d="M5 12l14 0"/>
                        </svg>
                        Add New Menu
                    </a>
                    <a href="<?= route_to('menu.create') ?>" class="btn btn-primary d-sm-none btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M12 5l0 14"/>
                            <path d="M5 12l14 0"/>
                        </svg>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <?php if (hasPermission('menu.dashboard')) : ?>
        <!-- Statistics Cards -->
        <div class="row row-deck row-cards mb-4">
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Total Menus</div>
                            <div class="ms-auto lh-1">
                                <div class="dropdown">
                                    <a class="dropdown-toggle text-muted" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item active" href="#">Last 7 days</a>
                                        <a class="dropdown-item" href="#">Last 30 days</a>
                                        <a class="dropdown-item" href="#">Last 3 months</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="h1 mb-3"><?= $stats['total'] ?></div>
                        <div class="d-flex mb-2">
                            <div>All menu items in the system</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Active Menus</div>
                        </div>
                        <div class="h1 mb-3 text-green"><?= $stats['active'] ?></div>
                        <div class="d-flex mb-2">
                            <div>Currently visible menu items</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Inactive Menus</div>
                        </div>
                        <div class="h1 mb-3 text-yellow"><?= $stats['inactive'] ?></div>
                        <div class="d-flex mb-2">
                            <div>Hidden menu items</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Root Menus</div>
                        </div>
                        <div class="h1 mb-3 text-blue"><?= $stats['root_menus'] ?></div>
                        <div class="d-flex mb-2">
                            <div>Top-level menu items</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row row-deck row-cards">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Menu Items</h3>
                        <div class="card-actions">
                            <?php if (hasPermission('menu.manage')) : ?>
                            <a href="<?= route_to('menu.create') ?>" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M12 5l0 14"/>
                                    <path d="M5 12l14 0"/>
                                </svg>
                                Add New Menu
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Flash Messages -->
                        <?php if (session()->getFlashdata('success')) : ?>
                        <div class="alert alert-success alert-dismissible show" role="alert">
                            <div class="d-flex">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M5 12l5 5l10 -10"/>
                                    </svg>
                                </div>
                                <div><?= session()->getFlashdata('success') ?></div>
                            </div>
                            <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                        </div>
                        <?php endif; ?>

                        <?php if (session()->getFlashdata('error')) : ?>
                        <div class="alert alert-danger alert-dismissible show" role="alert">
                            <div class="d-flex">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                                        <path d="M12 9v4"/>
                                        <path d="M12 17h.01"/>
                                    </svg>
                                </div>
                                <div><?= session()->getFlashdata('error') ?></div>
                            </div>
                            <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                        </div>
                        <?php endif; ?>

                        <!-- Bulk Actions -->
                        <?php if (hasPermission('menu.manage')) : ?>
                        <div class="d-flex mb-3">
                            <div class="btn-list">
                                <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M5 12l5 5l10 -10"/>
                                    </svg>
                                    Activate Selected
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M18 6l-12 12"/>
                                        <path d="M6 6l12 12"/>
                                    </svg>
                                    Deactivate Selected
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M4 7l16 0"/>
                                        <path d="M10 11l0 6"/>
                                        <path d="M14 11l0 6"/>
                                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                                        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                                    </svg>
                                    Delete Selected
                                </button>
                            </div>
                            <div class="ms-auto">
                                <span class="text-muted" id="selected-count">0 items selected</span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- DataTable -->
                        <div class="table-responsive">
                            <table id="menuTable" class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th width="30">#</th>
                                        <?php if (hasPermission('menu.manage')) : ?>
                                        <th style="width: 30px">
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <?php endif; ?>
                                        <th>Menu Label</th>
                                        <th>URL</th>
                                        <th>Roles</th>
                                        <th>Parent</th>
                                        <th>Status</th>
                                        <th class="w-1">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- DataTables CSS and JS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    const table = $('#menuTable').DataTable({
        processing: true,
        serverSide: true,
        pageLength: 25,
        responsive: true,
        language: {
            processing: '<div class="d-flex align-items-center"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...</div>',
            emptyTable: '<div class="empty"><div class="empty-img"><img src="<?= base_url("assets/img/undraw_printing_invoices_5r4r.svg") ?>" height="128" alt=""></div><p class="empty-title">No menu items found</p><p class="empty-subtitle text-muted">Try adjusting your search or filter to find what you\'re looking for.</p></div>',
            zeroRecords: '<div class="empty"><div class="empty-img"><img src="<?= base_url("assets/img/undraw_printing_invoices_5r4r.svg") ?>" height="128" alt=""></div><p class="empty-title">No matching records found</p><p class="empty-subtitle text-muted">Try adjusting your search or filter to find what you\'re looking for.</p></div>'
        },
        ajax: {
            url: '<?= site_url('admin/menu/datatable'); ?>',
            type: 'POST'
        },
        order: [['<?= hasPermission('menu.manage') ? 2 : 1 ?>', 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('menu.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'label' }
            ,{ data: 'url' }
            ,{ data: 'roles', orderable: false }
            ,{ data: 'parent_label' }
            ,{ data: 'active' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        drawCallback: function() {
            // Initialize tooltips after table draw
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });

    // Select all checkbox
    document.getElementById('select-all')?.addEventListener('change', function() {
        const checked = this.checked;
        document.querySelectorAll('.select-item').forEach(checkbox => {
            checkbox.checked = checked;
        });
        updateBulkButtons();
    });

    // Individual checkbox
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('select-item')) {
            updateBulkButtons();

            // Update select all checkbox
            const totalItems = document.querySelectorAll('.select-item').length;
            const checkedItems = document.querySelectorAll('.select-item:checked').length;
            const selectAllCheckbox = document.getElementById('select-all');

            if (selectAllCheckbox) {
                selectAllCheckbox.indeterminate = checkedItems > 0 && checkedItems < totalItems;
                selectAllCheckbox.checked = checkedItems === totalItems;
            }
        }
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        const selectedItems = document.querySelectorAll('.select-item:checked').length;
        const bulkButtons = document.querySelectorAll('#bulk-activate, #bulk-deactivate, #bulk-delete');
        const selectedCount = document.getElementById('selected-count');

        if (selectedItems > 0) {
            bulkButtons.forEach(btn => btn.disabled = false);
            if (selectedCount) {
                selectedCount.textContent = selectedItems + ' item' + (selectedItems > 1 ? 's' : '') + ' selected';
            }
        } else {
            bulkButtons.forEach(btn => btn.disabled = true);
            if (selectedCount) {
                selectedCount.textContent = '0 items selected';
            }
        }
    }

    // Bulk activate
    document.getElementById('bulk-activate')?.addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.select-item:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            showAlert('Please select items to activate', 'warning');
            return;
        }

        showConfirmDialog(
            'Activate Menus',
            `Are you sure you want to activate ${selectedIds.length} selected menu(s)?`,
            'success'
        ).then((confirmed) => {
            if (confirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-activate') ?>', selectedIds, 'activate');
            }
        });
    });

    // Bulk deactivate
    document.getElementById('bulk-deactivate')?.addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.select-item:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            showAlert('Please select items to deactivate', 'warning');
            return;
        }

        showConfirmDialog(
            'Deactivate Menus',
            `Are you sure you want to deactivate ${selectedIds.length} selected menu(s)?`,
            'warning'
        ).then((confirmed) => {
            if (confirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-deactivate') ?>', selectedIds, 'deactivate');
            }
        });
    });

    // Bulk delete
    document.getElementById('bulk-delete')?.addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.select-item:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            showAlert('Please select items to delete', 'warning');
            return;
        }

        showConfirmDialog(
            'Delete Menus',
            `Are you sure you want to delete ${selectedIds.length} selected menu(s)? This action cannot be undone!`,
            'danger'
        ).then((confirmed) => {
            if (confirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-delete') ?>', selectedIds, 'delete');
            }
        });
    });

    // Individual delete
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
            e.preventDefault();
            const button = e.target.classList.contains('btn-delete') ? e.target : e.target.closest('.btn-delete');
            const menuId = button.dataset.id;

            showConfirmDialog(
                'Delete Menu',
                'Are you sure you want to delete this menu? This action cannot be undone!',
                'danger'
            ).then((confirmed) => {
                if (confirmed) {
                    window.location.href = '<?= site_url('admin/menu/delete/') ?>/' + menuId;
                }
            });
        }
    });

    // Perform bulk action
    function performBulkAction(url, ids, action) {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                ids: ids,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                table.ajax.reload();
                document.getElementById('select-all').checked = false;
                updateBulkButtons();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while processing your request.', 'danger');
        });
    }

    // Helper functions for alerts and confirmations
    function showAlert(message, type = 'info') {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible show`;
        alertContainer.setAttribute('role', 'alert');
        alertContainer.innerHTML = `
            <div class="d-flex">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        ${type === 'success' ? '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/>' :
                          type === 'danger' ? '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/>' :
                          '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 9v4"/><path d="M12 17h.01"/>'}
                    </svg>
                </div>
                <div>${message}</div>
            </div>
            <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
        `;

        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertContainer, cardBody.firstChild);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    function showConfirmDialog(title, message, type = 'primary') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                            <div class="modal-title">${title}</div>
                            <div>${message}</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn me-auto" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-${type}" data-confirm="true">Confirm</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);

            modal.addEventListener('click', function(e) {
                if (e.target.dataset.confirm === 'true') {
                    resolve(true);
                    bsModal.hide();
                }
            });

            modal.addEventListener('hidden.bs.modal', function() {
                resolve(false);
                modal.remove();
            });

            bsModal.show();
        });
    }

    // Auto-hide alerts
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 500);
            }
        });
    }, 5000);
});
</script>
<?= $this->endSection() ?>