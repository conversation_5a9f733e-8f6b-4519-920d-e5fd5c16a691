<?= $this->extend('layouts/auth'); ?>

<?= $this->section('title'); ?><?= $title ?? 'Login - System'; ?><?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<div class="auth-logo">
    <img src="<?= base_url('assets/dist/img/citem_logo_2023.png') ?>" alt="System Logo">
    <h1><?= $system_name ?? 'System Maintenance Tool'; ?></h1>
    <p>Sign in to start your session</p>
</div>

<!-- Flash Messages -->
<?php if(session()->has('warning')): ?>
<div class="alert alert-warning alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M12 9v2m0 4v.01"/>
                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>
            </svg>
        </div>
        <div><?= session()->get('warning'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?php if(session()->getFlashdata('success')): ?>
<div class="alert alert-success alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M5 12l5 5l10 -10"/>
            </svg>
        </div>
        <div><?= session()->getFlashdata('success'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?php if(session()->getFlashdata('failed')): ?>
<div class="alert alert-danger alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                <path d="M12 9v4"/>
                <path d="M12 17h.01"/>
            </svg>
        </div>
        <div><?= session()->getFlashdata('failed'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?php if(session()->getFlashdata('error')): ?>
<div class="alert alert-danger alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                <path d="M12 9v4"/>
                <path d="M12 17h.01"/>
            </svg>
        </div>
        <div><?= session()->getFlashdata('error'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?= form_open(route_to('auth.login'), ['class' => 'mt-4']); ?>
    <?= csrf_field() ?>

    <!-- Username/Email Field -->
    <div class="mb-3">
        <label class="form-label"><?= $loginLabel ?? 'Username'; ?></label>
        <div class="form-floating-icon">
            <span class="form-icon">
                <?php if($loginField === 'email'): ?>
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"/>
                    <path d="M3 7l9 6l9 -6"/>
                </svg>
                <?php else: ?>
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"/>
                    <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                </svg>
                <?php endif; ?>
            </span>
            <input type="text"
                   class="form-control <?= session('errors.' . $loginField) ? 'is-invalid' : ''; ?>"
                   name="<?= $loginField ?>"
                   placeholder="Enter your <?= strtolower($loginLabel ?? 'username'); ?>"
                   value="<?= old($loginField); ?>"
                   required>
            <?php if(session('errors.' . $loginField)): ?>
            <div class="invalid-feedback">
                <?= session('errors.' . $loginField); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Password Field -->
    <div class="mb-3">
        <label class="form-label">Password</label>
        <div class="form-floating-icon">
            <span class="form-icon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M5 13a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-6z"/>
                    <path d="M11 16a1 1 0 1 0 2 0a1 1 0 0 0 -2 0"/>
                    <path d="M8 11v-4a4 4 0 1 1 8 0v4"/>
                </svg>
            </span>
            <input type="password"
                   id="password"
                   class="form-control <?= session('errors.password') ? 'is-invalid' : ''; ?>"
                   name="password"
                   placeholder="Enter your password"
                   required>
            <button type="button" class="password-toggle">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <use href="#tabler-eye"></use>
                </svg>
            </button>
            <?php if(session('errors.password')): ?>
            <div class="invalid-feedback">
                <?= session('errors.password'); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Submit Button -->
    <div class="mb-3">
        <button type="submit" class="btn btn-primary w-100 btn-auth">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"/>
                <path d="M20 12h-13l3 -3"/>
                <path d="M10 15l3 -3"/>
            </svg>
            Sign In
        </button>
    </div>
</form>

<!-- Auth Links -->
<div class="auth-links">
    <a href="<?= route_to('auth.forgotPassword'); ?>">Forgot your password?</a>
</div>

<?= $this->endSection(); ?>


<?= $this->section('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced password toggle with Tabler icons
    const passwordToggle = document.querySelector('.password-toggle');
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            const input = document.querySelector('#password');
            const icon = this.querySelector('svg use');

            if (input.type === 'password') {
                input.type = 'text';
                if (icon) {
                    icon.setAttribute('href', '#tabler-eye-off');
                }
            } else {
                input.type = 'password';
                if (icon) {
                    icon.setAttribute('href', '#tabler-eye');
                }
            }
        });
    }
});
</script>
<?= $this->endSection(); ?>