<?php

namespace Modules\MenuManagement\Config;

$routes->group('admin', function($routes) {
    $routes->group('menu', [
        'namespace' => 'Modules\MenuManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        $routes->GET('/', 'MenuController::index', ['as' => 'menu.index']);
        $routes->POST('datatable', 'MenuController::datatable', ['as' => 'menu.datatable']);
        $routes->GET('create', 'MenuController::create', ['as' => 'menu.create']);
        $routes->POST('store', 'MenuController::store', ['as' => 'menu.store']);
        $routes->GET('edit/(:num)', 'MenuController::edit/$1', ['as' => 'menu.edit']);
        $routes->POST('update/(:num)', 'MenuController::update/$1', ['as' => 'menu.update']);
        $routes->GET('delete/(:num)', 'MenuController::delete/$1', ['as' => 'menu.delete']);
        $routes->POST('bulk-delete', 'MenuController::bulkDelete', ['as' => 'menu.bulk_delete']);
        $routes->POST('bulk-activate', 'MenuController::bulkActivate', ['as' => 'menu.bulk_activate']);
        $routes->POST('bulk-deactivate', 'MenuController::bulkDeactivate', ['as' => 'menu.bulk_deactivate']);
        $routes->POST('reorder', 'MenuController::reorder', ['as' => 'menu.reorder']);
    });
});