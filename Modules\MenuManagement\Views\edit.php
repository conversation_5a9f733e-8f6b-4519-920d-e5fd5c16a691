<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <div class="page-pretitle">
                    Menu Management
                </div>
                <h2 class="page-title">
                    Edit Menu
                </h2>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="<?= route_to('menu.index') ?>" class="btn btn-outline-secondary d-none d-sm-inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l14 0"/>
                            <path d="M5 12l6 6"/>
                            <path d="M5 12l6 -6"/>
                        </svg>
                        Back to Menu List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <div class="row row-deck row-cards">
            <div class="col-12">
                <form action="<?= route_to('menu.update', $menu['id']); ?>" method="post">
                    <?= csrf_field() ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Menu Information</h3>
                        </div>
                        <div class="card-body">
                            <?php if (session()->has('errors')): ?>
                            <div class="alert alert-danger alert-dismissible show" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                                            <path d="M12 9v4"/>
                                            <path d="M12 17h.01"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Validation Error!</h4>
                                        <ul class="mb-0">
                                            <?php foreach (session('errors') as $error): ?>
                                                <li><?= esc($error) ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                </div>
                                <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                            </div>
                            <?php endif; ?>
                            <?php if (session()->getFlashdata('error')) : ?>
                            <div class="alert alert-danger alert-dismissible show" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                                            <path d="M12 9v4"/>
                                            <path d="M12 17h.01"/>
                                        </svg>
                                    </div>
                                    <div><?= session()->getFlashdata('error') ?></div>
                                </div>
                                <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                            </div>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label required">Menu Label</label>
                                        <input type="text" name="label" class="form-control <?= session('errors.label') ? 'is-invalid' : '' ?>"
                                               id="label" placeholder="Enter menu label" value="<?= old('label', $menu['label']) ?>" required>
                                        <small class="form-hint">The display name for the menu item</small>
                                        <?php if(session('errors.label')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.label') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label">URL</label>
                                        <input type="text" name="url" class="form-control <?= session('errors.url') ? 'is-invalid' : '' ?>"
                                               id="url" placeholder="e.g., admin/users" value="<?= old('url', $menu['url']) ?>">
                                        <small class="form-hint">Leave empty for parent menus with no direct link</small>
                                        <?php if(session('errors.url')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.url') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label">Icon Class</label>
                                        <input type="text" name="icon" class="form-control <?= session('errors.icon') ? 'is-invalid' : '' ?>"
                                               id="icon" placeholder="e.g., fas fa-users" value="<?= old('icon', $menu['icon']) ?>">
                                        <small class="form-hint">FontAwesome icon class (e.g., fas fa-users)</small>
                                        <?php if(session('errors.icon')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.icon') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sort Order</label>
                                        <input type="number" name="sort_order" class="form-control <?= session('errors.sort_order') ? 'is-invalid' : '' ?>"
                                               id="sort_order" placeholder="0" value="<?= old('sort_order', $menu['sort_order']) ?>" min="0">
                                        <small class="form-hint">Lower numbers appear first</small>
                                        <?php if(session('errors.sort_order')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.sort_order') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label">Parent Menu</label>
                                        <select name="parent_id" class="form-select <?= session('errors.parent_id') ? 'is-invalid' : '' ?>" id="parent_id">
                                            <option value="">-- Root Menu --</option>
                                            <?php if (!empty($parentMenus)): ?>
                                                <?php foreach ($parentMenus as $id => $label): ?>
                                                    <option value="<?= $id ?>" <?= old('parent_id', $menu['parent_id']) == $id ? 'selected' : '' ?>>
                                                        <?= esc($label) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                        <small class="form-hint">Select a parent menu to create a submenu</small>
                                        <?php if(session('errors.parent_id')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.parent_id') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label required">Required Roles</label>
                                        <select name="role_ids[]" id="role_ids" class="form-select <?= session('errors.role_ids') ? 'is-invalid' : '' ?>" multiple>
                                            <?php if (!empty($roles)): ?>
                                                <?php foreach ($roles as $role): ?>
                                                    <option value="<?= $role['id'] ?>" <?= in_array($role['id'], old('role_ids', $assignedRoleIds)) ? 'selected' : '' ?>>
                                                        <?= esc($role['name']) ?>
                                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                                        <small class="form-hint">Users with these roles can access this menu. Leave empty for public access.</small>
                                        <?php if(session('errors.role_ids')): ?>
                                        <div class="invalid-feedback">
                                            <?= session('errors.role_ids') ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-check">
                                    <input class="form-check-input" type="checkbox" id="active" name="active" value="1" <?= old('active', $menu['active']) ? 'checked' : '' ?>>
                                    <span class="form-check-label">Active</span>
                                </label>
                                <small class="form-hint">Inactive menus will not be displayed</small>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <div class="d-flex">
                                <a href="<?= route_to('menu.index') ?>" class="btn btn-link">Cancel</a>
                                <button type="submit" class="btn btn-primary ms-auto">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M5 12l5 5l10 -10"/>
                                    </svg>
                                    Update Menu
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Icon preview functionality
    const iconInput = document.getElementById('icon');
    if (iconInput) {
        iconInput.addEventListener('input', function() {
            const iconClass = this.value;
            let preview = document.getElementById('icon-preview');

            if (!preview) {
                preview = document.createElement('div');
                preview.id = 'icon-preview';
                preview.className = 'mt-2';
                this.parentNode.appendChild(preview);
            }

            if (iconClass) {
                preview.innerHTML = `<i class="${iconClass}"></i> Preview`;
            } else {
                preview.innerHTML = '';
            }
        });

        // Trigger initial preview
        iconInput.dispatchEvent(new Event('input'));
    }

    // Auto-hide alerts
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 500);
            }
        });
    }, 5000);

    // Form validation enhancement
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                // Show error message
                const alertContainer = document.createElement('div');
                alertContainer.className = 'alert alert-danger alert-dismissible show';
                alertContainer.setAttribute('role', 'alert');
                alertContainer.innerHTML = `
                    <div class="d-flex">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                                <path d="M12 9v4"/>
                                <path d="M12 17h.01"/>
                            </svg>
                        </div>
                        <div>Please fill in all required fields.</div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                `;

                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(alertContainer, cardBody.firstChild);

                // Scroll to top
                cardBody.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }
});
</script>
<?= $this->endSection() ?>
