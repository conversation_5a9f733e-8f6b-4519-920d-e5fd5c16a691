<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class DashboardController extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Dashboard',
            'page_header' => true,
            'page_pretitle' => 'Overview',
            'stats' => $this->getDashboardStats(),
            'recent_activities' => $this->getRecentActivities(),
            'chart_data' => $this->getChartData()
        ];

        return view('admin/dashboard', $data);
    }

    private function getDashboardStats()
    {
        // Sample statistics - replace with real data from your models
        return [
            [
                'title' => 'Total Users',
                'value' => '2,561',
                'change' => '+12%',
                'change_type' => 'positive',
                'icon' => 'ti ti-users',
                'color' => 'blue'
            ],
            [
                'title' => 'Revenue',
                'value' => '$74,382',
                'change' => '+5.4%',
                'change_type' => 'positive',
                'icon' => 'ti ti-currency-dollar',
                'color' => 'green'
            ],
            [
                'title' => 'Orders',
                'value' => '1,394',
                'change' => '-2.1%',
                'change_type' => 'negative',
                'icon' => 'ti ti-shopping-cart',
                'color' => 'yellow'
            ],
            [
                'title' => 'Conversion Rate',
                'value' => '3.65%',
                'change' => '+1.2%',
                'change_type' => 'positive',
                'icon' => 'ti ti-chart-line',
                'color' => 'purple'
            ]
        ];
    }

    private function getRecentActivities()
    {
        // Sample activities - replace with real data
        return [
            [
                'user' => 'John Doe',
                'action' => 'created a new user account',
                'time' => '2 minutes ago',
                'avatar' => 'tabler/img/avatars/000m.jpg'
            ],
            [
                'user' => 'Jane Smith',
                'action' => 'updated product inventory',
                'time' => '15 minutes ago',
                'avatar' => 'tabler/img/avatars/001f.jpg'
            ],
            [
                'user' => 'Mike Johnson',
                'action' => 'processed order #1234',
                'time' => '1 hour ago',
                'avatar' => 'tabler/img/avatars/002m.jpg'
            ],
            [
                'user' => 'Sarah Wilson',
                'action' => 'added new navigation menu',
                'time' => '2 hours ago',
                'avatar' => 'tabler/img/avatars/003f.jpg'
            ]
        ];
    }

    private function getChartData()
    {
        // Sample chart data - replace with real data
        return [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            'datasets' => [
                [
                    'label' => 'Sales',
                    'data' => [65, 59, 80, 81, 56, 55, 40],
                    'borderColor' => '#206bc4',
                    'backgroundColor' => 'rgba(32, 107, 196, 0.1)'
                ],
                [
                    'label' => 'Orders',
                    'data' => [28, 48, 40, 19, 86, 27, 90],
                    'borderColor' => '#79a6dc',
                    'backgroundColor' => 'rgba(121, 166, 220, 0.1)'
                ]
            ]
        ];
    }
}
