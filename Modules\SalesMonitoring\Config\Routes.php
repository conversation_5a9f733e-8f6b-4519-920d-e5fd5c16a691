<?php

namespace Modules\SalesMonitoring\Config;

$routes->group('sales', ['namespace' => 'Modules\SalesMonitoring\Controllers', 'filter' => 'login'], function ($routes) {
    $routes->get('/', 'SalesController::index', ['as' => 'sales.index']);
    $routes->get('create', 'SalesController::create', ['as' => 'sales.create']);
    $routes->post('store', 'SalesController::store', ['as' => 'sales.store']);
    $routes->get('edit/(:num)', 'SalesController::edit/$1', ['as' => 'sales.edit']);
    $routes->post('update/(:num)', 'SalesController::update/$1', ['as' => 'sales.update']);
    $routes->post('delete/(:num)', 'SalesController::delete/$1', ['as' => 'sales.delete']);
});


// $routes->group("exhibitor",["namespace"=>"App\Controllers\Exhibitor"], function($routes) {
//     // Dashboard
//     $routes->get('/', 'DashboardController::index'); // Redirect here after login

//     // Event selection
//     // $routes->get('events', 'EventController::list'); // List events exhibitor joined
//     //$routes->get('event/(:num)/select', 'EventController::select/$1'); // Select current event context
    
//     // Manage Sales During Event
//     $routes->GET('event/(:num)/sales', 'SalesController::index/$1'); // View all sales
//     // AJAX: Load sales into DataTables
//     $routes->GET('event/(:num)/sales/data', 'SalesController::dataTable/$1');
//     $routes->GET('event/(:num)/sales-data', 'SalesController::getSalesData/$1');
//     //$routes->get('event/(:num)/sales/create', 'SalesController::createForm/$1');
//     $routes->POST('event/(:num)/sales', 'SalesController::store/$1');
//     $routes->GET('event/(:num)/sales/(:num)/edit', 'SalesController::editForm/$1/$2');
//     $routes->POST('event/(:num)/sales/(:num)/update', 'SalesController::update/$1/$2');
//     $routes->POST('event/(:num)/sales/(:num)/delete', 'SalesController::delete/$1/$2');

//     // AJAX: Load sales into DataTables
//     $routes->GET('event/(:num)/inquiries/data', 'InquiryController::dataTable/$1');
//     $routes->POST('event/(:num)/inquiries', 'InquiryController::ajx_store/$1');
//     $routes->GET('event/(:num)/inquiries/(:num)/edit', 'InquiryController::editForm/$1/$2');
//     $routes->POST('event/(:num)/inquiries/(:num)', 'InquiryController::ajx_update/$1/$2');

//     // Update Sales After a Year
//     $routes->get('event/(:num)/sales/update', 'SalesController::updateList/$1'); // List to update
//     $routes->post('event/(:num)/sales/update/(:num)', 'SalesController::finalize/$1/$2'); // Finalize "under negotiation"
//     $routes->post('event/(:num)/sales/additional', 'SalesController::addAdditional/$1'); // Add post-event sales
// });
