<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="row row-deck row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Navigation Menu Management</h3>
                <div class="card-actions">
                    <a href="<?= base_url('admin/navigation/create') ?>" class="btn btn-primary">
                        <!-- Download SVG icon from http://tabler-icons.io/i/plus -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
                        Add New Menu
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($menus)): ?>
                <div class="empty">
                    <div class="empty-img"><img src="<?= base_url('tabler/img/undraw_printing_invoices_5r4r.svg') ?>" height="128" alt="">
                    </div>
                    <p class="empty-title">No navigation menus found</p>
                    <p class="empty-subtitle text-muted">
                        Try adjusting your search or filter to find what you're looking for.
                    </p>
                    <div class="empty-action">
                        <a href="<?= base_url('admin/navigation/create') ?>" class="btn btn-primary">
                            <!-- Download SVG icon from http://tabler-icons.io/i/plus -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
                            Add your first menu
                        </a>
                    </div>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>URL</th>
                                <th>Icon</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="menu-list">
                            <?php foreach ($menus as $menu): ?>
                            <?= renderMenuRow($menu, 0) ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
function renderMenuRow($menu, $level = 0) {
    $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
    $html = '<tr data-id="' . $menu['id'] . '">';
    $html .= '<td>' . $indent;
    if ($level > 0) {
        $html .= '<span class="text-muted">└─</span> ';
    }
    $html .= esc($menu['title']) . '</td>';
    $html .= '<td>' . ($menu['url'] ? '<code>' . esc($menu['url']) . '</code>' : '<span class="text-muted">No URL</span>') . '</td>';
    $html .= '<td>';
    if ($menu['icon']) {
        $html .= '<span class="' . esc($menu['icon']) . '"></span> ' . esc($menu['icon']);
    } else {
        $html .= '<span class="text-muted">No icon</span>';
    }
    $html .= '</td>';
    $html .= '<td><span class="badge bg-secondary">' . $menu['sort_order'] . '</span></td>';
    $html .= '<td>';
    if ($menu['is_active']) {
        $html .= '<span class="badge bg-success">Active</span>';
    } else {
        $html .= '<span class="badge bg-danger">Inactive</span>';
    }
    $html .= '</td>';
    $html .= '<td>';
    $html .= '<div class="btn-list flex-nowrap">';
    $html .= '<a href="' . base_url('admin/navigation/edit/' . $menu['id']) . '" class="btn btn-white btn-sm">Edit</a>';
    $html .= '<button class="btn btn-white btn-sm" onclick="deleteMenu(' . $menu['id'] . ')">Delete</button>';
    $html .= '</div>';
    $html .= '</td>';
    $html .= '</tr>';
    
    if (!empty($menu['children'])) {
        foreach ($menu['children'] as $child) {
            $html .= renderMenuRow($child, $level + 1);
        }
    }
    
    return $html;
}
?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function deleteMenu(id) {
    if (confirm('Are you sure you want to delete this menu item?')) {
        fetch('<?= base_url('admin/navigation/delete') ?>/' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Failed to delete menu item');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the menu item');
        });
    }
}
</script>
<?= $this->endSection() ?>
