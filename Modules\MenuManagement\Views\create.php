<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- Choices.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css">
<style>

    /* .choices__list--dropdown {
        background-color: #fff !important;
        opacity: 1 !important;
        backdrop-filter: none !important;
    }

    .choices__list--dropdown {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        border: 1px solid #ccc;
    } */


    /* .choices__inner {
        min-height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        background-color: #fff;
    }
    .choices[data-type*="select-multiple"] .choices__inner {
        cursor: text;
    }
    .choices__item--choice.is-highlighted {
        background-color: #007bff;
    }
    .choices__item--selectable.is-highlighted {
        background-color: #007bff;
    }
    .is-invalid .choices__inner {
        border-color: #dc3545;
    }
    .choices__item--choice {
        padding: 8px 12px;
    }
    .choices__item--choice:hover {
        background-color: #f8f9fa;
    }
    .border-warning .choices__inner {
        border-color: #ffc107 !important;
    } */

    /* .choices__item--selectable {
        background-color: #000000ff !important;
        color: #fff !important;
    } */
    
</style>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= route_to('menu.index') ?>">Menu Management</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Create New Menu</h3>
    </div>
    <form action="<?= route_to('menu.store'); ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="label">Menu Label <span class="text-danger">*</span></label>
                        <input type="text" name="label" class="form-control" id="label" placeholder="Enter menu label" value="<?= old('label') ?>" required>
                        <small class="form-text text-muted">The display name for the menu item</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="url">URL</label>
                        <input type="text" name="url" class="form-control" id="url" placeholder="e.g., admin/users" value="<?= old('url') ?>">
                        <small class="form-text text-muted">Leave empty for parent menus with no direct link</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="icon">Icon Class</label>
                        <input type="text" name="icon" class="form-control" id="icon" placeholder="e.g., fas fa-users" value="<?= old('icon') ?>">
                        <small class="form-text text-muted">FontAwesome icon class (e.g., fas fa-users)</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" name="sort_order" class="form-control" id="sort_order" placeholder="0" value="<?= old('sort_order', 0) ?>" min="0">
                        <small class="form-text text-muted">Lower numbers appear first</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="parent_id">Parent Menu</label>
                        <select name="parent_id" class="form-control" id="parent_id">
                            <option value="">-- Root Menu --</option>
                            <?php if (!empty($parentMenus)): ?>
                                <?php foreach ($parentMenus as $id => $label): ?>
                                    <option value="<?= $id ?>" <?= old('parent_id') == $id ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small class="form-text text-muted">Select a parent menu to create a submenu</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="role_ids">Required Roles <span class="text-danger">*</span></label>
                        <select name="role_ids[]" id="role_ids" class="form-control" multiple="multiple" data-placeholder="Select roles that can access this menu">
                            <?php if (!empty($roles)): ?>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?= $role['id'] ?>" <?= in_array($role['id'], old('role_ids', [])) ? 'selected' : '' ?>>
                                        <?= esc($role['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small class="form-text text-muted">Users with these roles can access this menu. Leave empty for public access.</small>
                    </div>
                </div>

                <!-- <div class="col-md-6">
                    <div class="form-group">
                        <label for="permission_id">Legacy Permission (Optional)</label>
                        <select name="permission_id" class="form-control" id="permission_id">
                            <option value="">-- No Permission Required --</option>
                            <?php //if (!empty($permissions)): ?>
                                <?php //foreach ($permissions as $permission): ?>
                                    <option value="<?//= $permission['id'] ?>" <?//= old('permission_id') == $permission['id'] ? 'selected' : '' ?>>
                                        <?//= esc($permission['name']) ?>
                                        <?php //if (!empty($permission['description'])): ?>
                                            - <?//= esc($permission['description']) ?>
                                        <?php //endif; ?>
                                    </option>
                                <?php //endforeach; ?>
                            <?php //endif; ?>
                        </select>
                        <small class="form-text text-muted">Legacy permission system (for backward compatibility)</small>
                    </div>
                </div> -->

            </div>

            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input class="custom-control-input" type="checkbox" id="active" name="active" value="1" <?= old('active', '1') ? 'checked' : '' ?>>
                    <label for="active" class="custom-control-label">Active</label>
                    <small class="form-text text-muted">Inactive menus will not be displayed</small>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">Create Menu</button>
            <a href="<?= route_to('menu.index') ?>" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- Choices.js JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Choices.js for role selection
    const roleChoices = new Choices('#role_ids', {
        removeItemButton: true,
        placeholder: true,
        placeholderValue: 'Select roles that can access this menu',
        searchPlaceholderValue: 'Search roles...',
        noResultsText: 'No roles found',
        itemSelectText: 'Press to select',
        classNames: {
            containerOuter: 'choices',
            containerInner: 'choices__inner',
            input: 'choices__input',
            inputCloned: 'choices__input--cloned',
            list: 'choices__list',
            listItems: 'choices__list--multiple',
            listSingle: 'choices__list--single',
            listDropdown: 'choices__list--dropdown',
            item: 'choices__item',
            itemSelectable: 'choices__item--selectable',
            itemDisabled: 'choices__item--disabled',
            itemChoice: 'choices__item--choice',
            placeholder: 'choices__placeholder',
            group: 'choices__group',
            groupHeading: 'choices__heading',
            button: 'choices__button',
            activeState: 'is-active',
            focusState: 'is-focused',
            openState: 'is-open',
            disabledState: 'is-disabled',
            highlightedState: 'is-highlighted',
            selectedState: 'is-selected',
            flippedState: 'is-flipped',
            loadingState: 'is-loading',
            noResults: 'has-no-results',
            noChoices: 'has-no-choices'
        }
    });

    // Icon preview
    $('#icon').on('input', function() {
        var iconClass = $(this).val();
        var preview = $('#icon-preview');
        if (preview.length === 0) {
            $(this).after('<div id="icon-preview" class="mt-2"></div>');
            preview = $('#icon-preview');
        }

        if (iconClass) {
            preview.html('<i class="' + iconClass + '"></i> Preview');
        } else {
            preview.html('');
        }
    });

    // Trigger initial preview
    $('#icon').trigger('input');
});
</script>
<?= $this->endSection() ?>
