<?= $this->extend('layouts/auth'); ?>

<?= $this->section('title'); ?>Register - System<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<div class="auth-logo">
    <img src="<?= base_url('assets/dist/img/citem_logo_2023.png') ?>" alt="System Logo">
    <h1>Create Account</h1>
    <p>Join our system to get started</p>
</div>

<!-- Flash Messages -->
<?php if(session()->has('warning')): ?>
<div class="alert alert-warning alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M12 9v2m0 4v.01"/>
                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>
            </svg>
        </div>
        <div><?= session()->get('warning'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?php if(session()->getFlashdata('error')): ?>
<div class="alert alert-danger alert-dismissible show" role="alert">
    <div class="d-flex">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"/>
                <path d="M12 9v4"/>
                <path d="M12 17h.01"/>
            </svg>
        </div>
        <div><?= session()->getFlashdata('error'); ?></div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?= form_open(route_to('auth.register'), ['class' => 'mt-4']); ?>
    <?= csrf_field() ?>

    <!-- Username Field -->
    <div class="mb-3">
        <label class="form-label">Username</label>
        <div class="form-floating-icon">
            <span class="form-icon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"/>
                    <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                </svg>
            </span>
            <input type="text"
                   class="form-control <?= session('errors.username') ? 'is-invalid' : ''; ?>"
                   name="username"
                   placeholder="Enter your username"
                   value="<?= old('username'); ?>"
                   required>
            <?php if(session('errors.username')): ?>
            <div class="invalid-feedback">
                <?= session('errors.username'); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Email Field -->
    <div class="mb-3">
        <label class="form-label">Email</label>
        <div class="form-floating-icon">
            <span class="form-icon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"/>
                    <path d="M3 7l9 6l9 -6"/>
                </svg>
            </span>
            <input type="email"
                   class="form-control <?= session('errors.email') ? 'is-invalid' : ''; ?>"
                   name="email"
                   placeholder="Enter your email address"
                   value="<?= old('email'); ?>"
                   required>
            <?php if(session('errors.email')): ?>
            <div class="invalid-feedback">
                <?= session('errors.email'); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Password Field -->
    <div class="mb-3">
        <label class="form-label">Password</label>
        <div class="form-floating-icon">
            <span class="form-icon">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M5 13a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-6z"/>
                    <path d="M11 16a1 1 0 1 0 2 0a1 1 0 0 0 -2 0"/>
                    <path d="M8 11v-4a4 4 0 1 1 8 0v4"/>
                </svg>
            </span>
            <input type="password"
                   id="password"
                   class="form-control <?= session('errors.password') ? 'is-invalid' : ''; ?>"
                   name="password"
                   placeholder="Enter your password"
                   required>
            <button type="button" class="password-toggle">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <use href="#tabler-eye"></use>
                </svg>
            </button>
            <?php if(session('errors.password')): ?>
            <div class="invalid-feedback">
                <?= session('errors.password'); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Submit Button -->
    <div class="mb-3">
        <button type="submit" class="btn btn-primary w-100 btn-auth">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M12 5l0 14"/>
                <path d="M5 12l14 0"/>
            </svg>
            Create Account
        </button>
    </div>
</form>

<!-- Auth Links -->
<div class="auth-links">
    <a href="<?= route_to('auth.login'); ?>">Already have an account? Sign in</a>
</div>

<?= $this->endSection(); ?>
